<script setup lang="ts">
const props = defineProps({
  orientation: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'vertical'
  },
  thumbnailUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  preset: {
    type: String,
    default: 'Unknown'
  },
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  duration: {
    type: String,
    default: '8s'
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits<{
  'select-another-voice': []
}>()

const { models, model } = useSpeechGenModels()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()

// Find the corresponding model label for the preset value
const presetLabel = computed(() => {
  const modelFound = models.find(m => m.value === props.data?.model_name)
  return modelFound ? modelFound.label : props.preset
})

const historyStore = useHistoryStore()
const { showDetailModal, historyDetail } = storeToRefs(historyStore)
const lastGenerated = computed(() => {
  return (
    props.data?.generated_audio?.[props.data?.generated_audio?.length - 1] || {}
  )
})

const baseInfoProperties = computed(() => {
  const properties: Record<string, any> = {
    model: presetLabel.value,
    emotion: t(props.data?.emotion || ''),
    estimated_credit: props.data?.estimated_credit,
    output_format: t(props.data?.output_format || ''),
    voice: lastGenerated.value?.voices?.[0]?.name
  }

  // Only include used_credit if not in beta mode
  if (!runtimeConfig.public.features.beta && props.data?.used_credit) {
    properties.used_credit = props.data.used_credit
  }

  return properties
})

const audioUrl = computed(() => {
  return lastGenerated.value?.audio_url || ''
})

const onSelectAnotherVoice = () => {
  emit('select-another-voice')
}

const gotoDetail = async () => {
  await navigateTo(`/history?filter_by=${props.data.type}&uuid=${props.data.uuid}`)
  // Small delay to ensure navigation is complete before opening modal
  await nextTick()
  historyDetail.value = props.data as any
  showDetailModal.value = true
}
</script>

<template>
  <UPageCard
    :title="title"
    :orientation="orientation"
    :ui="{
      container: 'lg:items-start flex-col-reverse',
      body: 'w-full h-full',
      wrapper: 'h-full',
      description: 'h-full flex flex-col'
    }"
    class="h-full"
    reverse
  >
    <div
      v-if="loading || data.status === 1"
      class="w-full h-[230px] lg:h-56 bg-gray-200 dark:bg-neutral-800 rounded-lg flex items-center justify-center"
    >
      <UIcon
        name="svg-spinners:blocks-wave"
        class="w-16 h-16 dark:text-neutral-700"
      />
    </div>
    <div
      v-else
      class="relative w-full group cursor-pointer"
    >
      <WaveformPlayer
        v-if="audioUrl"
        :audio-url="audioUrl"
        :fullscreen="true"
      />
      <div
        v-else-if="data?.status === 3"
        class="w-full h-64 text-error gap-2 lg:h-80 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center"
      >
        <UIcon
          name="material-symbols:error"
          class="w-10 h-10"
        />
        <div class="flex flex-col gap-1 items-center justify-center">
          <div>
            {{ $t("ui.errors.generationFailed") }}
          </div>
          <div class="text-xs px-4 lg:px-6 text-error/70">
            {{ data?.error_message }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="w-full h-64 lg:h-80 bg-gray-200 dark:bg-neutral-800 rounded-lg flex items-center justify-center"
      >
        <UIcon
          name="hugeicons:voice"
          class="w-16 h-16 text-gray-400"
        />
      </div>
    </div>

    <template #title>
      <div class="line-clamp-2">
        {{ title }}
      </div>
    </template>

    <template #description>
      <div class="text-xs mt-2">
        <div class="font-bold line-clamp-2">
          <div>{{ $t("Text") }}</div>
        </div>
        <div class="font-light p-2 bg-muted mt-1 rounded-lg line-clamp-4">
          {{ data?.input_text }}
        </div>
      </div>
      <BaseInfo
        class="mt-4"
        :properties="baseInfoProperties"
      />
      <div
        v-if="data?.status === 1"
        class="mt-4"
      >
        <UAlert
          :title="$t('Tips')"
          :description="
            $t(
              'Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.'
            )
          "
          color="neutral"
          variant="soft"
          icon="icon-park-twotone:info"
          :actions="[
            {
              label: $t('Go to History'),
              color: 'primary',
              variant: 'subtle',
              onClick: gotoDetail
            },
            {
              label: $t('Select Another Voice'),
              color: 'neutral',
              variant: 'solid',
              onClick: onSelectAnotherVoice
            }
          ]"
        />
      </div>
      <div
        v-if="audioUrl"
        class="mt-4 flex gap-2 justify-between"
      >
        <BaseDownloadButton
          :link="audioUrl"
          :label="$t('downloadAudio')"
          color="primary"
          class="w-fit"
        />
        <UButton
          color="neutral"
          :label="$t('Select Another Voice')"
          class="w-fit"
          @click="onSelectAnotherVoice"
        />
      </div>
    </template>
  </UPageCard>
</template>
