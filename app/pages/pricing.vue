<script setup lang="ts">
definePageMeta({
  middleware: 'beta-feature'
})

// SEO meta tags for pricing page
useSeoMeta({
  title: 'Pricing - Affordable AI Content Generation Plans',
  description: 'Simple and flexible pricing for AI image generation, video creation, and text-to-speech. Pay only for what you use with our credit-based system.',
  ogTitle: 'GeminiGen AI Pricing - Affordable AI Content Generation',
  ogDescription: 'Discover our transparent pricing for AI image generation, video creation, and text-to-speech services. Start free or choose pay-as-you-go plans.',
  keywords: 'AI pricing, image generation cost, video generation pricing, text-to-speech rates, AI content creation plans'
})

// Add structured data for better SEO
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Product',
        'name': 'GeminiGen AI Content Generation',
        'description': 'AI-powered image generation, video creation, and text-to-speech services',
        'brand': {
          '@type': 'Brand',
          'name': 'GeminiGen AI'
        },
        'offers': [
          {
            '@type': 'Offer',
            'name': 'Free Plan',
            'price': '0',
            'priceCurrency': 'USD',
            'description': 'Free tier with limited usage'
          },
          {
            '@type': 'Offer',
            'name': 'Premium Plan',
            'description': 'Pay-as-you-go pricing for unlimited access',
            'priceCurrency': 'USD'
          }
        ]
      })
    }
  ]
})

const { t } = useI18n()
const productStore = useProductStore()
const { getServicePriceByModelName, oneUSDCredits } = storeToRefs(productStore)
const authStore = useAuthStore()
const showMode = ref('money') // money | credits
const appStore = useAppStore()
const { adsBanner } = storeToRefs(appStore)
const showModeItems = computed(() => {
  return [
    {
      label: t('Show money'),
      value: 'money',
      icon: 'solar:dollar-bold'
    },
    {
      label: t('Show credits'),
      value: 'credits',
      icon: 'akar-icons:coin'
    }
  ]
})

const tiers = computed(() => {
  // Access store getters with type assertion to avoid TypeScript issues
  const isFreeUser = (authStore as any).isFreeUser
  const isPremiumUser = (authStore as any).isPremiumUser
  const isAuthenticated = (authStore as any).isAuthenticated

  return [
    {
      id: 'free',
      title: t('Free'),
      price: '$0',
      description: t('Forever'),
      button: {
        label: isFreeUser
          ? t('Current plan')
          : isPremiumUser
            ? t('Already premium')
            : t('Get started'),
        variant: 'soft' as const,
        color: 'neutral' as const,
        onclick: () => {
          if (isAuthenticated) {
            navigateTo('/profile/credits')
          } else {
            navigateTo('/auth/login')
          }
        }
      }
    },
    {
      id: 'premium',
      title: t('Premium'),
      description: t('Auto upgrade after buy credits'),
      price: t('Pay as you go'),
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'primary' as const,
        onclick: () => navigateTo('/profile/credits')
      },
      highlight: true
    },
    {
      id: 'enterprise',
      title: t('Enterprise'),
      price: t('Contact us'),
      description: t('For large organizations.'),
      button: {
        label: t('Contact sales'),
        variant: 'soft' as const,
        color: 'neutral' as const
      }
    }
  ]
})

const sections = computed(() => {
  return [
    {
      id: 'imagen',
      title: t('Imagen'),
      features: [
        {
          id: 'imagen-flash',
          title: t('Gemini 2.0 Flash'),
          tiers: {
            free: {
              priceUnit: 0.049, // $0.049/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-flash')
                  ?.effective_price
            },
            premium: {
              priceUnit: 0.049, // $0.049/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-flash')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4-fast',
          title: t('Imagen 4 Fast'),
          tiers: {
            free: {
              priceUnit: 0.025, // $0.025/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-fast')
                  ?.effective_price
            },
            premium: {
              priceUnit: 0.025, // $0.025/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-fast')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4',
          title: t('Imagen 4  '),
          tiers: {
            free: {
              priceUnit: 0.05, // $0.05/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4')?.effective_price
            },
            premium: {
              priceUnit: 0.05, // $0.05/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4-ultra',
          title: t('Imagen 4 Ultra'),
          tiers: {
            free: {
              priceUnit: 0.075, // $0.075/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-ultra')
                  ?.effective_price
            },
            premium: {
              priceUnit: 0.075, // $0.075/image (adjusted for 60% discount)
              discount: 0.4, // 60% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-ultra')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'image-style',
          title: t('Image Style'),
          tiers: {
            free: t('{n}+ Styles', { n: 15 }),
            premium: t('{n}+ Styles', { n: 15 }),
            enterprise: t('{n}+ Styles', { n: 15 })
          }
        },
        // Aspect Ratio
        {
          id: 'image-dimensions',
          title: t('Image Aspect Ratio'),
          tiers: {
            free: t('Support multiple aspect ratio'),
            premium: t('Support multiple aspect ratio'),
            enterprise: t('Support multiple aspect ratio')
          }
        },
        {
          id: 'image-reference',
          title: t('Image Reference'),
          tiers: {
            free: t('Up to {size}MB', { size: 10 }),
            premium: t('Up to {size}MB', { size: 10 }),
            enterprise: t('Up to {size}MB', { size: 10 })
          }
        }
      ]
    },
    {
      id: 'video-gen',
      title: t('Video Gen'),
      features: [
        {
          id: 'veo-2-hd',
          title: t('Veo 2 HD'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.125, // $0.125/second (75% off from $0.50)
              discount: 0, // Already discounted price
              unit: t('second'),
              credits:
                getServicePriceByModelName.value('veo-2')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3-fast-hd',
          title: t('Veo 3 Fast HD (With Audio)'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.16, // $0.16/second (60% off from $0.40)
              discount: 0, // Already discounted price
              unit: t('second'),
              credits:
                getServicePriceByModelName.value('veo-3-fast')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3-fast-fullhd',
          title: t('Veo 3 Fast Full HD (With Audio)'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.24, // $0.24/second (40% off from $0.40)
              discount: 0, // Already discounted price
              unit: t('second'),
              credits:
                getServicePriceByModelName.value('veo-3-fast')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3-hd',
          title: t('Veo 3 HD'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.30, // $0.30/second (60% off from $0.75)
              discount: 0, // Already discounted price
              unit: t('second'),
              credits:
                getServicePriceByModelName.value('veo-3')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3-fullhd',
          title: t('Veo 3 Full HD'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.45, // $0.45/second (40% off from $0.75)
              discount: 0, // Already discounted price
              unit: t('second'),
              credits:
                getServicePriceByModelName.value('veo-3')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'videogen-image-reference',
          title: t('Image Reference'),
          tiers: {
            free: false,
            premium: t('Up to {size}MB', { size: 10 }),
            enterprise: t('Up to {size}MB', { size: 10 })
          }
        },
        // Enhance Prompt
        {
          id: 'videogen-enhance-prompt',
          title: t('Enhance Prompt'),
          tiers: {
            free: false,
            premium: t('Support enhance prompt'),
            enterprise: t('Support enhance prompt')
          }
        },
        // Aspect Ratio
        {
          id: 'videogen-aspect-ratio',
          title: t('Aspect Ratio'),
          tiers: {
            free: false,
            premium: t('Support multiple aspect ratio'),
            enterprise: t('Support multiple aspect ratio')
          }
        }
      ]
    },
    {
      id: 'speech-gen',
      title: `${t('Speech Gen')}, ${t('Dialogue Gen')}`,
      features: [
        {
          id: 'gemini-2.5-flash',
          title: t('Gemini 2.5 Flash'),
          tiers: {
            free: {
              priceUnit: 20, // $0.5/video
              discount: 0, // 50% discount
              unit: t('1M characters'),
              credits:
                getServicePriceByModelName.value('tts-flash')?.effective_price
                * 1000000
            },
            premium: {
              priceUnit: 20, // $0.5/video
              discount: 0, // 50% discount
              unit: t('1M characters'),
              credits:
                getServicePriceByModelName.value('tts-flash')?.effective_price
                * 1000000
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'gemini-2.5-pro',
          title: t('Gemini 2.5 Pro'),
          tiers: {
            free: {
              priceUnit: 40, // $0.5/video
              discount: 0, // 50% discount
              unit: t('1M characters'),
              credits:
                getServicePriceByModelName.value('tts-pro')?.effective_price
                * 1000000
            },
            premium: {
              priceUnit: 40, // $0.5/video
              discount: 0, // 50% discount
              unit: t('1M characters'),
              credits:
                getServicePriceByModelName.value('tts-pro')?.effective_price
                * 1000000
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'voice',
          title: t('Voice'),
          tiers: {
            free: t('Support {n}+ voices', { n: 400 }),
            premium: t('Support {n}+ voices', { n: 400 }),
            enterprise: t('Support {n}+ voices', { n: 400 })
          }
        },
        {
          id: 'emotion',
          title: t('Emotion'),
          tiers: {
            free: t('Support {n}+ emotions', { n: 100 }),
            premium: t('Support {n}+ emotions', { n: 100 }),
            enterprise: t('Support {n}+ emotions', { n: 100 })
          }
        },
        {
          id: 'language',
          title: t('Language'),
          tiers: {
            free: t('Support {n}+ languages', { n: 100 }),
            premium: t('Support {n}+ languages', { n: 100 }),
            enterprise: t('Support {n}+ languages', { n: 100 })
          }
        },
        {
          id: 'custom-prompt',
          title: t('Custom Prompt'),
          tiers: {
            free: t('Support custom prompt'),
            premium: t('Support custom prompt'),
            enterprise: t('Support custom prompt')
          }
        },
        {
          id: 'output-format',
          title: t('Output Format'),
          tiers: {
            free: t('Support MP3 and WAV'),
            premium: t('Support MP3 and WAV'),
            enterprise: t('Support MP3 and WAV')
          }
        },
        {
          id: 'speed',
          title: t('Speed'),
          tiers: {
            free: t('Support speed control'),
            premium: t('Support speed control'),
            enterprise: t('Support speed control')
          }
        },
        {
          id: 'document',
          title: t('Document'),
          tiers: {
            free: t('Support document to speech'),
            premium: t('Support document to speech'),
            enterprise: t('Support document to speech')
          }
        }
      ]
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <!-- SEO H1 Heading -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {{ $t('Pricing') }}
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          {{ $t('Simple and flexible. Only pay for what you use.') }}
        </p>
      </div>

      <UPageHeader
        :title="$t('pricing.title')"
        :description="$t('Simple and flexible. Only pay for what you use.')"
        class="sr-only"
      >
        <template #links />
      </UPageHeader>
    </UContainer>
    <div
      v-if="adsBanner"
      class="sticky top-16 z-20 bg-warning-50 dark:bg-warning-800 border-warning-200 dark:border-warning-800 border-b"
    >
      <div class="flex items-center gap-3 px-4 py-3">
        <!-- Icon -->

        <!-- Scrolling Title Container -->
        <div
          ref="titleContainerRef"
          class="flex-1 overflow-hidden relative"
        >
          <div
            ref="titleRef"
            :class="[
              'text-sm text-center items-center flex justify-center sm:justify-start pl-4 gap-2 text-warning-900 dark:text-warning-100 font-medium whitespace-wrap'
            ]"
          >
            <UIcon
              v-if="adsBanner.icon"
              :name="adsBanner.icon"
              class="text-warning-500 dark:text-warning-200 flex-shrink-0"
              size="20"
            />
            {{ $t(adsBanner.title) }}
          </div>
        </div>
      </div>
    </div>
    <UPageSection
      :ui="{
        container: '!pt-10'
      }"
    >
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div
          class="lg:col-span-3 lg:col-start-2 flex flex-col justify-center items-center gap-2"
        >
          <div class="flex flex-col justify-center items-center gap-0">
            <h2 class="text-xl font-bold">
              {{
                $t("${price} = {n} credits", {
                  price: 1,
                  n: formatNumber(oneUSDCredits)
                })
              }}
            </h2>
            <p class="text-xs text-muted">
              {{
                $t(
                  "You can switch between money and credits to see the price in your preferred currency."
                )
              }}
            </p>
          </div>
          <UTabs
            v-model="showMode"
            color="neutral"
            :content="false"
            :items="showModeItems"
            class="w-fit"
            size="xs"
          />
        </div>
      </div>
      <UPricingTable
        :tiers="tiers"
        :sections="sections"
        class="bg-muted dark:bg-transparent rounded-2xl pl-4 pb-10"
      >
        <!-- Customize specific tier title -->
        <template #premium-title="{ tier }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-crown"
              class="size-4 text-amber-500"
            />
            {{ tier.title }}
          </div>
        </template>

        <!-- Customize specific section title -->
        <template #section-security-title="{ section }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-shield-check"
              class="size-4 text-green-500"
            />
            <span class="font-semibold text-green-700">{{
              section.title
            }}</span>
          </div>
        </template>

        <!-- Customize specific feature value -->
        <template #feature-value="{ feature, tier }">
          <template v-if="feature.tiers?.[tier.id] === true">
            <UIcon
              name="prime:check-circle"
              class="size-5 text-primary"
            />
          </template>
          <template v-else-if="feature.tiers?.[tier.id]?.priceUnit">
            <div
              class="group flex flex-row items-center relative w-full justify-between gap-1"
            >
              <div class="flex flex-row items-end">
                <div>
                  <div
                    class="text-lg sm:text-2xl lg:text-3xl font-semibold scale-x-90 scale-y-110 group-hover:text-primary"
                  >
                    <span v-if="showMode === 'money'">
                      ${{
                        (feature.tiers[tier.id].priceUnit
                          * (feature.tiers[tier.id].discount || 1)).toFixed(4).replace(/\.?0+$/, '')
                      }}
                    </span>
                    <span v-else>
                      {{
                        feature.tiers[tier.id].credits > 10000
                          ? abbreviatedUnit(feature.tiers[tier.id].credits)
                          : formatNumber(feature.tiers[tier.id].credits)
                      }}
                    </span>
                  </div>
                </div>
                <div class="text-left">
                  <div
                    v-if="feature.tiers[tier.id].discount"
                    class="flex items-center gap-1"
                  >
                    <span class="relative inline-block">
                      <span class="px-0.5 relative z-10 text-muted opacity-70">
                        {{
                          showMode === "money"
                            ? "$" + feature.tiers[tier.id].priceUnit.toFixed(4).replace(/\.?0+$/, '')
                            : t("{n} credits", {
                              n:
                                Math.round(feature.tiers[tier.id].credits
                                  / feature.tiers[tier.id].discount)
                            })
                        }}
                      </span>
                      <span
                        class="absolute top-1/2 left-0 right-0 border-t border-gray- dark:border-gray-500 z-0"
                      />
                    </span>
                  </div>
                  <div class="text-xs font-semibold group-hover:text-primary">
                    {{
                      showMode === "money"
                        ? t("USD / {unit}", {
                          unit: t(feature.tiers[tier.id].unit || "")
                        })
                        : t("Credits / {unit}", {
                          unit: t(feature.tiers[tier.id].unit || "")
                        })
                    }}
                  </div>
                </div>
              </div>
              <UBadge
                v-if="feature.tiers[tier.id].discount"
                color="success"
                size="sm"
                variant="soft"
                class="transition-all duration-200 group-hover:scale-150"
              >
                {{
                  feature.tiers[tier.id].discount
                    ? t("Save {n}%", {
                      n: Math.round(
                        (1 - feature.tiers[tier.id].discount) * 100
                      )
                    })
                    : ""
                }}
              </UBadge>
            </div>
          </template>
          <div
            v-else-if="feature.tiers?.[tier.id]"
            class="text-left w-full"
          >
            {{ feature.tiers[tier.id] }}
          </div>
          <UIcon
            v-else
            name="i-lucide-x"
            class="size-5 text-muted"
          />
        </template>
      </UPricingTable>
      <!-- Video Resolution Pricing Details -->
      <div class="mt-12 mb-12">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {{ $t('Video Generation Pricing by Resolution') }}
          </h2>
          <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {{ $t('Different video resolutions have different pricing. Higher resolutions cost more but provide better quality.') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <!-- Veo 3 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold mb-4 text-center">
              {{ $t('Veo 3') }}
            </h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('Base Price') }}:</span>
                <span class="font-semibold">$0.75/{{ $t('second') }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('HD Resolution') }}:</span>
                <span class="font-semibold text-green-600">$0.30/{{ $t('second') }} (60% {{ $t('off') }})</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('Full HD Resolution') }}:</span>
                <span class="font-semibold text-blue-600">$0.45/{{ $t('second') }} (40% {{ $t('off') }})</span>
              </div>
            </div>
          </div>

          <!-- Veo 3 Fast -->
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold mb-4 text-center">
              {{ $t('Veo 3 Fast') }}
            </h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('Base Price') }}:</span>
                <span class="font-semibold">$0.40/{{ $t('second') }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('HD Resolution') }}:</span>
                <span class="font-semibold text-green-600">$0.16/{{ $t('second') }} (60% {{ $t('off') }})</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('Full HD Resolution') }}:</span>
                <span class="font-semibold text-blue-600">$0.24/{{ $t('second') }} (40% {{ $t('off') }})</span>
              </div>
            </div>
          </div>

          <!-- Veo 2 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-semibold mb-4 text-center">
              {{ $t('Veo 2') }}
            </h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('Base Price') }}:</span>
                <span class="font-semibold">$0.50/{{ $t('second') }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-300">{{ $t('HD Resolution Only') }}:</span>
                <span class="font-semibold text-green-600">$0.125/{{ $t('second') }} (75% {{ $t('off') }})</span>
              </div>
              <div class="text-center text-sm text-gray-500 dark:text-gray-400 mt-2">
                {{ $t('Full HD not available for Veo 2') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Calculator Section -->
      <div class="mt-12">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {{ $t('Pricing Calculator') }}
          </h2>
          <p class="text-lg text-gray-600 dark:text-gray-300 max-w-xl mx-auto">
            {{ $t('Calculate how many resources can you generate with your budget.') }}
          </p>
        </div>
        <BasePricingCalculator />
      </div>

      <!-- Import BuyCreditsDrawer -->
      <BuyCreditsDrawer />
    </UPageSection>
  </UPage>
</template>
