<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

const props = defineProps({
  orientation: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'vertical'
  },
  thumbnailUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  preset: {
    type: String,
    default: 'Unknown'
  },
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  duration: {
    type: String,
    default: '8s'
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const { models, model } = useVideoGenModels()
const historyStore = useHistoryStore()
const { showDetailModal, historyDetail } = storeToRefs(historyStore)
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()

const isFullScreenOpen = ref(false)
const isVideoPlaying = ref(false)

// Find the corresponding model label for the preset value
const presetLabel = computed(() => {
  const modelFound = models.find(m => m.value === props.data?.model_name)
  return modelFound ? modelFound.label : props.preset
})

const openFullScreen = () => {
  if (props.data.status === 1 || props.data.status === 3) return
  isFullScreenOpen.value = true
}

const onVideoLoaded = (event: any) => {
  console.log(event?.message || t('ui.messages.videoLoaded'))
}

const onVideoProcessing = (event: any) => {
  console.log(event?.message || t('ui.messages.videoProcessing'))
}

const getPersonGenerationLabel = (value: string) => {
  const options = [
    { value: 'DONT_ALLOW', label: t('personGeneration.dontAllow') },
    { value: 'ALLOW_ADULT', label: t('personGeneration.allowAdult') },
    { value: 'ALLOW_ALL', label: t('personGeneration.allowAll') }
  ]
  return options.find(option => option.value === value)?.label || value
}

const lastGenerated = computed(() => {
  return (
    props.data?.generated_video?.[props.data?.generated_video?.length - 1] || {}
  )
})

const videoUrl = computed(() => {
  return lastGenerated.value?.video_url || ''
})

const baseInfoProperties = computed(() => {
  const properties: Record<string, any> = {
    model: t(presetLabel.value),
    aspectRatio: props.data?.aspect_ratio,
    duration: props.duration,
    enhance_prompt: t(props.data?.enhance_prompt ? t('On') : t('Off')),
    negative_prompt: props.data?.negative_prompt
  }

  // Add resolution if available
  if (props.data?.resolution) {
    properties.resolution = props.data.resolution
  }

  // Only include used_credit if not in beta mode
  if (!runtimeConfig.public.features.beta && props.data?.used_credit) {
    properties.used_credit = props.data.used_credit
  }

  return properties
})

const gotoDetail = async () => {
  await navigateTo(`/history?filter_by=video&uuid=${props.data.uuid}`)
  // Small delay to ensure navigation is complete before opening modal
  await nextTick()
  historyDetail.value = props.data as any
  showDetailModal.value = true
}
</script>

<template>
  <UPageCard
    :title="title"
    :orientation="orientation"
    :ui="{
      container: 'lg:items-start flex-col-reverse',
      body: 'w-full h-full',
      wrapper: 'h-full',
      description: 'h-full flex flex-col'
    }"
    class="h-full"
    reverse
  >
    <div
      v-if="loading || data.status === 1"
      class="w-full h-40 lg:h-56 bg-gray-200 dark:bg-neutral-800 rounded-lg flex items-center justify-center"
    >
      <UIcon
        name="svg-spinners:blocks-wave"
        class="w-16 h-16 dark:text-neutral-700"
      />
    </div>
    <div
      v-else
      class="relative w-full group cursor-pointer"
      @click="openFullScreen"
    >
      <video
        v-if="videoUrl"
        :src="videoUrl"
        :poster="thumbnailUrl"
        class="w-full h-64 lg:h-80 object-cover rounded-lg"
        muted
        loop
        @mouseenter="$event.target.play()"
        @mouseleave="$event.target.pause()"
        @loadeddata="onVideoLoaded"
        @loadstart="onVideoProcessing"
      />
      <img
        v-else-if="thumbnailUrl"
        :src="thumbnailUrl"
        :alt="title"
        class="w-full h-64 lg:h-80 object-cover rounded-lg"
      >

      <div
        v-else-if="data?.status === 3"
        class="w-full h-64 text-error gap-2 lg:h-80 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center"
      >
        <UIcon
          name="material-symbols:error"
          class="w-10 h-10"
        />
        <div class="flex flex-col gap-1 items-center justify-center">
          <div>
            {{ $t("ui.errors.generationFailed") }}
          </div>
          <div class="text-xs px-4 lg:px-6 text-error/70">
            {{ data?.error_message }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="w-full h-64 lg:h-80 bg-gray-200 dark:bg-neutral-800 rounded-lg flex items-center justify-center"
      >
        <UIcon
          name="i-lucide-video"
          class="w-16 h-16 text-gray-400"
        />
      </div>
      <!-- Play button overlay -->
      <div
        v-if="videoUrl"
        class="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <UIcon
          name="i-lucide-play"
          class="w-12 h-12 text-white"
        />
      </div>
    </div>

    <template #title>
      <div class="line-clamp-2">
        {{ title }}
      </div>
    </template>

    <template #description>
      <div class="text-xs mt-2">
        <div class="font-bold line-clamp-2">
          <div>{{ $t("promptDetails") }}</div>
        </div>
        <div class="font-light p-2 bg-muted mt-1 rounded-lg">
          {{ data?.input_text }}
        </div>
      </div>
      <BaseInfo
        class="mt-4"
        :properties="baseInfoProperties"
      />
      <div
        v-if="data?.status === 1"
        class="mt-4"
      >
        <UAlert
          :title="$t('Tips')"
          :description="
            $t(
              'Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.'
            )
          "
          color="neutral"
          variant="soft"
          icon="icon-park-twotone:info"
          :actions="[
            {
              label: $t('Go to History'),
              color: 'primary',
              variant: 'subtle',
              onClick: gotoDetail
            }
          ]"
        />
      </div>
      <BaseDownloadButton
        :link="videoUrl"
        :label="$t('downloadVideo')"
        color="primary"
        class="mt-4 w-fit"
      />
    </template>
  </UPageCard>

  <!-- Full Screen Video Modal -->
  <UModal
    v-model:open="isFullScreenOpen"
    fullscreen
    :ui="{
      content: 'bg-black/90 backdrop-blur-xl'
    }"
    @keydown.esc="isFullScreenOpen = false"
  >
    <template #content>
      <div
        class="relative w-full h-full flex items-center justify-center"
        @click="isFullScreenOpen = false"
      >
        <!-- Prevent click propagation on the video itself to avoid closing when clicking on the video -->
        <video
          v-if="videoUrl"
          :src="videoUrl"
          :poster="thumbnailUrl"
          class="max-h-[90vh] max-w-[90vw] object-contain cursor-auto"
          controls
          autoplay
          @click.stop
          @play="isVideoPlaying = true"
          @pause="isVideoPlaying = false"
        />
        <img
          v-else-if="thumbnailUrl"
          :src="thumbnailUrl"
          :alt="title"
          class="max-h-[90vh] max-w-[90vw] object-contain cursor-zoom-out"
          @click.stop
        >
        <UButton
          icon="i-lucide-x"
          color="neutral"
          variant="ghost"
          class="absolute top-4 right-4 text-white hover:bg-white/10"
          @click="isFullScreenOpen = false"
        />
        <div class="absolute bottom-4 text-white/70 text-sm">
          {{ $t("clickToClose") }}
        </div>
      </div>
    </template>
  </UModal>
</template>
